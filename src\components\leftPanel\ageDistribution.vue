<!-- 智能问答 -->
<template>
  <CPanel class="age-distribution">
    <template #header>智能问答</template>
    <template #content>
      <CEcharts ref="chartRef" :option="option" @onload="startHighlightLoop" />
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref<any>({})
const chartRef = ref()
let highlightTimer: any = null
let currentIndex = 0
const VALUE = [100, 200, 300, 400, 500, 600, 700]

const createEchartBar = () => {
  const xAxisData = ['早(8:00~12:00)', '中(12:00~18:00)', '晚(18:00~24:00)']
  const seriesData = [
    {
      value: 100
    },
    {
      value: 200
    },
    {
      value: 300
    },
  ]

  let maxAmount = 0
  seriesData.map(item => {
    item.value > maxAmount ? (maxAmount = item.value) : (maxAmount = maxAmount)
  })

  return {
    grid: {
      left: '0%',
      right: '0%',
      top: '15%',
      bottom: '0',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          width: 2,
          color: 'rgba(76, 93, 130, 1)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: '#FFFFFF',
        textStyle: {
          color: '#C5D6E6'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      nameTextStyle: {
        color: 'rgba(201, 211, 234, 1)',
        fontSize: 14,
        padding: [0, 32, 12, 0]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(52, 71, 112, 1)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 14,
        color: '#C5D6E6',
        textStyle: {
          color: '#C5D6E6',
          fontSize: 14
        }
      }
    },
    series: [
      {
        type: 'pictorialBar',
        name: '渐变背景',
        barWidth: 14,
        symbol: 'rect',
        symbolSize: '100%',
        symbolPosition: 'start',
        symbolOffset: [0, 0],
        label: {
          normal: {
            show: true,
            position: 'top',
            formatter: (params: any) => {
              return [...Object.values(seriesData[params.dataIndex])].join('\n')
            },
            fontSize: 12,
            lineHeight: 16,
            color: '#93B9FF'
          }
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(45, 205, 162, 1)'
              },
              {
                offset: 1,
                color: 'rgba(34, 150, 120, 1)'
              }
            ])
          }
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(45, 205, 162, 0.8)'
              },
              {
                offset: 1,
                color: 'rgba(45, 205, 162, 0.4)'
              }
            ])
          }
        },
        z: 1,
        zlevel: 0,
        data: seriesData.map(item => item.value)
      },
      {
        type: 'pictorialBar',
        name: '块状切片',
        itemStyle: {
          normal: {
            color: '#011140'
          }
        },
        barWidth: 14,
        symbolRepeat: 28,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [14, 2],
        symbolPosition: 'start',
        symbolOffset: [0, 0],
        data: seriesData.map(item => item.value),
        z: 2,
        zlevel: 0
      }
    ]
  }
}

// 高亮循环方法
const startHighlightLoop = (chart: any) => {
  if (!chart) return

  // 如果已经存在定时器，先清除
  if (highlightTimer) {
    clearInterval(highlightTimer)
    highlightTimer = null
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart.dispatchAction({
      type: 'downplay'
    })
    // 高亮当前柱子
    chart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex
    })
    // 更新索引，循环
    currentIndex = (currentIndex + 1) % VALUE.length
  }, 1500)
}

onMounted(() => {
  option.value = createEchartBar()
})
onUnmounted(() => {
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>
<style lang="scss" scoped></style>
